import axios from 'axios'
import { parseStrEmpty } from "@/utils/ruoyi";
import { getToken } from '@/utils/auth'

// 创建axios实例
const miniappRequest = axios.create({
  // 小程序API的基础URL
  baseURL: 'http://127.0.0.1:9001/app/java',
  // 超时
  timeout: 10000
})

// request拦截器
miniappRequest.interceptors.request.use(config => {
  // 添加token
  if (getToken()) {
    config.headers['Authorization'] = 'Bearer ' + getToken()
  }
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})

// 响应拦截器
miniappRequest.interceptors.response.use(
  response => {
    // 直接返回数据
    return response.data
  },
  error => {
    console.log('err' + error)
    return Promise.reject(error)
  }
)

// 查询历史活动列表
export function listPreviousEvent(query) {
  return miniappRequest({
    url: '/miniapp/previous/all',
    method: 'get',
    params: query
  })
}

// 查询历史活动详细
export function getPreviousEvent(previousEventId) {
  return miniappRequest({
    url: '/miniapp/previous/' + parseStrEmpty(previousEventId),
    method: 'get'
  })
}

// 新增历史活动
export function addPreviousEvent(file, previousEventTitle, previousEventIsOnline, previousEventTime,
  previousEventDescription, previousEventLikesCount, previousEventTag, previousEventLocation,
  previousEventIsShown, previousEventSort, previousEventLikesOffset) {

  const formData = new FormData();
  formData.append('file', file);
  formData.append('previousEventTitle', previousEventTitle);
  formData.append('previousEventIsOnline', previousEventIsOnline);
  formData.append('previousEventTime', previousEventTime);
  formData.append('previousEventDescription', previousEventDescription);
  formData.append('previousEventLikesCount', previousEventLikesCount);
  formData.append('previousEventTag', previousEventTag);
  formData.append('previousEventLocation', previousEventLocation);
  formData.append('previousEventIsShown', previousEventIsShown);
  formData.append('previousEventSort', previousEventSort);
  formData.append('previousEventLikesOffset', previousEventLikesOffset);

  return miniappRequest({
    url: '/miniapp/previous',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传历史活动图片
export function uploadPreviousEventImage(file, previousEventDescription, previousEventId) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('previousEventDescription', previousEventDescription);
  formData.append('previousEventId', previousEventId);

  return miniappRequest({
    url: '/miniapp/previous/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}