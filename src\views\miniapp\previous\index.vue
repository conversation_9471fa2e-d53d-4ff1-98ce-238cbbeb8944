<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--历史活动数据-->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>历史活动管理</span>
          </div>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
              >新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleBatchDelete"
              >删除</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>

          <el-table v-loading="loading" :data="previousEventList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="活动ID" align="center" prop="previousEventId" width="100" />
            <el-table-column label="活动标题" align="center" prop="previousEventTitle" />
            <el-table-column label="活动类型" align="center" prop="previousEventTag" width="120" />
            <el-table-column label="活动时间" align="center" width="180">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.previousEventTime) }}
              </template>
            </el-table-column>
            <el-table-column label="活动地点" align="center" prop="previousEventLocation" width="150" />
            <el-table-column label="线上/线下" align="center" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.previousEventIsOnline ? 'success' : 'info'">
                  {{ scope.row.previousEventIsOnline ? '线上' : '线下' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="是否展示" align="center" width="100">
              <template slot-scope="scope">
                <el-tag :type="scope.row.previousEventIsShown ? 'success' : 'info'">
                  {{ scope.row.previousEventIsShown ? '展示' : '隐藏' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="排序" align="center" prop="previousEventSort" width="80" />
            <el-table-column label="点赞数" align="center" prop="previousEventLikesCount" width="80" />
            <el-table-column label="操作" width="250" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="mini" icon="el-icon-view" @click="handlePreview(scope.row)">预览</el-button>
                <el-button type="text" size="mini" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
                <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加历史活动对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="活动标题" prop="previousEventTitle">
          <el-input v-model="form.previousEventTitle" placeholder="请输入活动标题" />
        </el-form-item>
        <el-form-item label="活动类型" prop="previousEventTag">
          <el-input v-model="form.previousEventTag" placeholder="请输入活动类型" />
        </el-form-item>
        <el-form-item label="活动时间" prop="previousEventTime">
          <el-date-picker
            v-model="form.previousEventTime"
            type="datetime"
            placeholder="选择活动时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="活动地点" prop="previousEventLocation">
          <el-input v-model="form.previousEventLocation" placeholder="请输入活动地点" />
        </el-form-item>
        <el-form-item label="活动描述" prop="previousEventDescription">
          <el-input v-model="form.previousEventDescription" type="textarea" placeholder="请输入活动描述" />
        </el-form-item>
        <el-form-item label="线上/线下" prop="previousEventIsOnline">
          <el-switch v-model="form.previousEventIsOnline" />
        </el-form-item>
        <el-form-item label="是否展示" prop="previousEventIsShown">
          <el-switch v-model="form.previousEventIsShown" />
        </el-form-item>
        <el-form-item label="排序" prop="previousEventSort">
          <el-input-number v-model="form.previousEventSort" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="点赞数" prop="previousEventLikesCount">
          <el-input-number v-model="form.previousEventLikesCount" :min="0" />
        </el-form-item>
        <el-form-item label="点赞偏移量" prop="previousEventLikesOffset">
          <el-input-number v-model="form.previousEventLikesOffset" :min="0" />
        </el-form-item>
        <el-form-item label="活动头像" prop="file">
          <el-upload
            class="avatar-uploader"
            action="#"
            :http-request="handleFormHttpRequest"
            :before-upload="handleFormBeforeUpload"
            :limit="1"
            :on-exceed="handleFormExceed"
            :file-list="formFileList"
            :on-change="handleFormFileChange"
            :auto-upload="false"
            list-type="picture-card">
            <i class="el-icon-plus"></i>
            <div slot="tip" class="el-upload__tip">
              <span style="color: #F56C6C; font-weight: bold;">*必选项</span> - 只能上传jpg/png文件，且不超过2MB
            </div>
          </el-upload>
          <!-- 显示图片预览 -->
          <el-image
            v-if="imageUrl"
            style="width: 100px; height: 100px; margin-top: 10px;"
            :src="imageUrl"
            fit="cover">
          </el-image>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="formLoading">确 定</el-button>
        <el-button @click="cancel" :disabled="formLoading">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 预览历史活动对话框 -->
    <el-dialog :title="'历史活动详情 - ' + previewForm.previousEventTitle" :visible.sync="openPreview" width="800px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="活动ID">{{ previewForm.previousEventId }}</el-descriptions-item>
        <el-descriptions-item label="活动标题">{{ previewForm.previousEventTitle }}</el-descriptions-item>
        <el-descriptions-item label="活动类型">{{ previewForm.previousEventTag }}</el-descriptions-item>
        <el-descriptions-item label="活动时间">{{ formatDateTime(previewForm.previousEventTime) }}</el-descriptions-item>
        <el-descriptions-item label="活动地点">{{ previewForm.previousEventLocation }}</el-descriptions-item>
        <el-descriptions-item label="活动描述">{{ previewForm.previousEventDescription }}</el-descriptions-item>
        <el-descriptions-item label="线上/线下">
          <el-tag :type="previewForm.previousEventIsOnline ? 'success' : 'info'">
            {{ previewForm.previousEventIsOnline ? '线上' : '线下' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="是否展示">
          <el-tag :type="previewForm.previousEventIsShown ? 'success' : 'info'">
            {{ previewForm.previousEventIsShown ? '展示' : '隐藏' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="排序">{{ previewForm.previousEventSort }}</el-descriptions-item>
        <el-descriptions-item label="点赞数">{{ previewForm.previousEventLikesCount }}</el-descriptions-item>
        <el-descriptions-item label="点赞偏移量">{{ previewForm.previousEventLikesOffset }}</el-descriptions-item>
        <el-descriptions-item label="活动头像">
          <div style="display: flex; align-items: center; gap: 10px;">
            <el-image
              style="width: 100px; height: 100px;"
              :src="getImageUrl(previewForm.previousEventAvatar)"
              :preview-src-list="[getImageUrl(previewForm.previousEventAvatar)]"
              fit="cover">
            </el-image>
            <el-button type="primary" size="mini" icon="el-icon-picture" @click="handleReplaceAvatar">替换头像</el-button>
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <div style="margin-top: 20px;">
        <div class="clearfix">
          <span>活动图片</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="handleImageUpload">上传图片</el-button>
        </div>
        <el-divider></el-divider>
        <div v-if="!previewForm.images || previewForm.images.length === 0" class="empty-block">
          暂无图片
        </div>
        <el-row :gutter="10" v-else>
          <el-col :span="8" v-for="(image, index) in previewForm.images" :key="index" style="margin-bottom: 10px;">
            <el-card :body-style="{ padding: '0px' }">
              <el-image
                style="width: 100%; height: 150px;"
                :src="getImageUrl(image.previousEventImageUrl)"
                :preview-src-list="[getImageUrl(image.previousEventImageUrl)]"
                fit="cover">
              </el-image>
              <div style="padding: 14px;">
                <span>{{ image.previousEventDescription }}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="openPreview = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 修改历史活动对话框 -->
    <el-dialog :title="'修改历史活动 - ' + updateForm.previousEventTitle" :visible.sync="openUpdate" width="600px" append-to-body>
      <el-form ref="updateForm" :model="updateForm" :rules="updateRules" label-width="120px">
        <el-form-item label="活动标题" prop="previousEventTitle">
          <el-input v-model="updateForm.previousEventTitle" placeholder="请输入活动标题" />
        </el-form-item>
        <el-form-item label="活动类型" prop="previousEventTag">
          <el-input v-model="updateForm.previousEventTag" placeholder="请输入活动类型" />
        </el-form-item>
        <el-form-item label="活动时间" prop="previousEventTime">
          <el-date-picker
            v-model="updateForm.previousEventTime"
            type="datetime"
            placeholder="选择活动时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="活动地点" prop="previousEventLocation">
          <el-input v-model="updateForm.previousEventLocation" placeholder="请输入活动地点" />
        </el-form-item>
        <el-form-item label="活动描述" prop="previousEventDescription">
          <el-input v-model="updateForm.previousEventDescription" type="textarea" placeholder="请输入活动描述" />
        </el-form-item>
        <el-form-item label="线上/线下" prop="previousEventIsOnline">
          <el-switch v-model="updateForm.previousEventIsOnline" />
        </el-form-item>
        <el-form-item label="是否展示" prop="previousEventIsShown">
          <el-switch v-model="updateForm.previousEventIsShown" />
        </el-form-item>
        <el-form-item label="排序" prop="previousEventSort">
          <el-input-number v-model="updateForm.previousEventSort" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="点赞数" prop="previousEventLikesCount">
          <el-input-number v-model="updateForm.previousEventLikesCount" :min="0" />
        </el-form-item>
        <el-form-item label="点赞偏移量" prop="previousEventLikesOffset">
          <el-input-number v-model="updateForm.previousEventLikesOffset" :min="0" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpdateForm" :loading="updateLoading">确 定</el-button>
        <el-button @click="cancelUpdate" :disabled="updateLoading">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 替换头像对话框 -->
    <el-dialog title="替换活动头像" :visible.sync="openReplaceAvatar" width="500px" append-to-body>
      <el-form ref="avatarForm" :model="avatarForm" label-width="100px">
        <el-form-item label="活动标题">
          <span>{{ avatarForm.previousEventTitle }}</span>
        </el-form-item>
        <el-form-item label="当前头像">
          <el-image
            style="width: 100px; height: 100px;"
            :src="getImageUrl(avatarForm.currentAvatar)"
            fit="cover">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </el-form-item>
        <el-form-item label="选择新头像" prop="file">
          <el-upload
            class="upload-demo"
            action="#"
            :http-request="handleAvatarHttpRequest"
            :before-upload="beforeAvatarUpload"
            :limit="1"
            :on-exceed="handleAvatarExceed"
            :file-list="avatarFileList"
            :on-change="handleAvatarFileChange"
            :auto-upload="false"
            list-type="picture">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAvatarUpload" :loading="avatarLoading">确 定</el-button>
        <el-button @click="cancelAvatarUpload" :disabled="avatarLoading">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 上传图片对话框 -->
    <el-dialog :title="'上传活动图片 - ' + uploadForm.previousEventTitle" :visible.sync="openUpload" width="500px" append-to-body>
      <el-form ref="uploadForm" :model="uploadForm" label-width="100px">
        <el-form-item label="活动ID">
          <span>{{ uploadForm.previousEventId }}</span>
        </el-form-item>
        <el-form-item label="活动标题">
          <span>{{ uploadForm.previousEventTitle }}</span>
        </el-form-item>
        <el-form-item label="图片描述">
          <el-input v-model="uploadForm.previousEventDescription" placeholder="请输入图片描述"></el-input>
        </el-form-item>
        <el-form-item label="图片" prop="file">
          <el-upload
            class="upload-demo"
            action="#"
            :http-request="handleImageFileUpload"
            :before-upload="beforeUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
            list-type="picture">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUploadForm" :loading="uploadLoading">确 定</el-button>
        <el-button @click="cancelUpload" :disabled="uploadLoading">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPreviousEvent,
  getPreviousEvent,
  addPreviousEvent,
  uploadPreviousEventImage,
  uploadPreviousEventAvatar,
  updatePreviousEvent
} from "@/api/miniapp/previous";
import RightToolbar from "@/components/RightToolbar";
import Pagination from "@/components/Pagination";

export default {
  name: "MiniappPreviousEvent",
  components: {
    RightToolbar,
    Pagination
  },
  data() {
    return {
      // OSS基础URL
      ossBaseUrl: "https://xiuzhe-weixinapp.oss-cn-shanghai.aliyuncs.com/",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 历史活动表格数据
      previousEventList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {
        previousEventId: undefined,
        previousEventTitle: undefined,
        previousEventIsOnline: true,
        previousEventTime: undefined,
        previousEventDescription: undefined,
        previousEventLikesCount: 0,
        previousEventTag: undefined,
        previousEventLocation: undefined,
        previousEventIsShown: true,
        previousEventSort: 1,
        previousEventLikesOffset: 0,
        file: null
      },
      // 图片URL
      imageUrl: '',
      // 表单文件列表
      formFileList: [],
      // 表单校验
      rules: {
        previousEventTitle: [
          { required: true, message: "活动标题不能为空", trigger: "blur" }
        ],
        previousEventTag: [
          { required: true, message: "活动类型不能为空", trigger: "blur" }
        ],
        previousEventTime: [
          { required: true, message: "活动时间不能为空", trigger: "blur" }
        ],
        previousEventLocation: [
          { required: true, message: "活动地点不能为空", trigger: "blur" }
        ],
        previousEventDescription: [
          { required: true, message: "活动描述不能为空", trigger: "blur" }
        ],
        file: [
          { required: true, message: "请选择活动头像图片", trigger: "change" }
        ]
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 预览对话框
      openPreview: false,
      // 预览表单数据
      previewForm: {
        previousEventId: undefined,
        previousEventTitle: undefined,
        previousEventIsOnline: true,
        previousEventTime: undefined,
        previousEventDescription: undefined,
        previousEventLikesCount: 0,
        previousEventTag: undefined,
        previousEventLocation: undefined,
        previousEventIsShown: true,
        previousEventSort: 1,
        previousEventLikesOffset: 0,
        previousEventAvatar: undefined,
        images: []
      },
      // 上传图片对话框
      openUpload: false,
      // 上传加载状态
      uploadLoading: false,
      // 表单提交加载状态
      formLoading: false,
      // 上传文件列表
      fileList: [],
      // 上传表单
      uploadForm: {
        file: null,
        previousEventId: undefined,
        previousEventTitle: undefined,
        previousEventDescription: ''
      },
      // 修改对话框
      openUpdate: false,
      // 修改加载状态
      updateLoading: false,
      // 修改表单
      updateForm: {
        previousEventId: undefined,
        previousEventTitle: undefined,
        previousEventIsOnline: true,
        previousEventTime: undefined,
        previousEventDescription: undefined,
        previousEventLikesCount: 0,
        previousEventTag: undefined,
        previousEventLocation: undefined,
        previousEventIsShown: true,
        previousEventSort: 1,
        previousEventLikesOffset: 0
      },
      // 修改表单校验
      updateRules: {
        previousEventTitle: [
          { required: true, message: "活动标题不能为空", trigger: "blur" }
        ],
        previousEventTag: [
          { required: true, message: "活动类型不能为空", trigger: "blur" }
        ],
        previousEventTime: [
          { required: true, message: "活动时间不能为空", trigger: "blur" }
        ],
        previousEventLocation: [
          { required: true, message: "活动地点不能为空", trigger: "blur" }
        ],
        previousEventDescription: [
          { required: true, message: "活动描述不能为空", trigger: "blur" }
        ]
      },
      // 替换头像对话框
      openReplaceAvatar: false,
      // 头像上传加载状态
      avatarLoading: false,
      // 头像文件列表
      avatarFileList: [],
      // 头像表单
      avatarForm: {
        previousEventId: undefined,
        previousEventTitle: '',
        currentAvatar: '',
        file: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 获取图片完整URL */
    getImageUrl(image) {
      if (!image) {
        return "";
      }
      // 如果已经是完整URL，直接返回
      if (image.startsWith('http')) {
        return image;
      }
      // 否则拼接OSS基础URL
      return this.ossBaseUrl + image;
    },
    /** 查询历史活动列表 */
    getList() {
      this.loading = true;
      listPreviousEvent(this.queryParams).then(response => {
        // 根据实际API返回格式调整
        if (response.data) {
          this.previousEventList = response.data;
          this.total = response.data.length;
        } else {
          this.previousEventList = [];
          this.total = 0;
        }
        this.loading = false;
      }).catch(error => {
        console.error("获取历史活动列表失败:", error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        previousEventId: undefined,
        previousEventTitle: undefined,
        previousEventIsOnline: true,
        previousEventTime: undefined,
        previousEventDescription: undefined,
        previousEventLikesCount: 0,
        previousEventTag: undefined,
        previousEventLocation: undefined,
        previousEventIsShown: true,
        previousEventSort: 1,
        previousEventLikesOffset: 0,
        file: null
      };
      this.imageUrl = '';
      // 确保清空文件列表
      this.formFileList = [];
      // 重置表单验证
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.previousEventId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加历史活动";
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除历史活动编号为"' + row.previousEventId + '"的数据项?').then(() => {
        this.$modal.msgSuccess("删除成功");
        this.getList();
      }).catch(() => {});
    },
    /** 表单自定义上传请求 */
    handleFormHttpRequest() {
      // 这个方法不会被调用，因为我们设置了 auto-upload 为 false
      // 但是需要提供这个方法以避免 el-upload 报错
    },
    /** 表单上传前检查 */
    handleFormBeforeUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
        return false;
      }

      // 保存文件到表单数据
      this.form.file = file;

      // 更新文件列表，使上传组件显示已选择的文件
      this.formFileList = [{
        name: file.name,
        url: URL.createObjectURL(file),
        raw: file // 保存原始文件对象
      }];

      // 更新预览图片
      this.imageUrl = URL.createObjectURL(file);

      console.log('文件已选择:', file.name);
      return false; // 阻止自动上传
    },
    /** 表单文件数量超出限制时的钩子 */
    handleFormExceed() {
      this.$message.error('最多只能上传1张图片');
    },
    /** 表单文件变更时的钩子 */
    handleFormFileChange(file) {
      if (file && file.raw) {
        // 确保文件被保存到表单中
        this.form.file = file.raw;
        console.log('文件已更新:', file.name);
      }
    },
    /** 上传前检查 */
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    /** 文件数量超出限制 */
    handleExceed() {
      this.$message.error('最多只能上传1张图片!');
    },
    /** 批量删除按钮操作 */
    handleBatchDelete() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请先选择要删除的历史活动");
        return;
      }
      this.$modal.confirm('是否确认删除选中的' + this.ids.length + '条历史活动数据?').then(() => {
        this.$modal.msgSuccess("批量删除成功");
        this.getList();
      }).catch(() => {});
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 从文件列表或表单中获取文件对象
          let fileToUpload = this.form.file;

          // 如果form.file为空但文件列表不为空，则从文件列表中获取文件
          if (!fileToUpload && this.formFileList.length > 0 && this.formFileList[0].raw) {
            fileToUpload = this.formFileList[0].raw;
            // 更新form.file以确保一致性
            this.form.file = fileToUpload;
          }

          if (!fileToUpload) {
            this.$message.error('请选择活动头像图片!');
            return;
          }

          console.log('准备上传文件:', fileToUpload.name);

          this.formLoading = true;
          addPreviousEvent(
            fileToUpload,
            this.form.previousEventTitle,
            this.form.previousEventIsOnline,
            this.form.previousEventTime,
            this.form.previousEventDescription,
            this.form.previousEventLikesCount,
            this.form.previousEventTag,
            this.form.previousEventLocation,
            this.form.previousEventIsShown,
            this.form.previousEventSort,
            this.form.previousEventLikesOffset
          ).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            } else {
              this.$modal.msgError(response.msg || "新增失败");
            }
            this.formLoading = false;
          }).catch(error => {
            console.error("新增历史活动失败:", error);
            this.$modal.msgError("新增失败，请重试");
            this.formLoading = false;
          });
        }
      });
    },
    /** 预览按钮操作 */
    handlePreview(row) {
      this.previewForm = {
        previousEventId: undefined,
        previousEventTitle: undefined,
        previousEventIsOnline: true,
        previousEventTime: undefined,
        previousEventDescription: undefined,
        previousEventLikesCount: 0,
        previousEventTag: undefined,
        previousEventLocation: undefined,
        previousEventIsShown: true,
        previousEventSort: 1,
        previousEventLikesOffset: 0,
        previousEventAvatar: undefined,
        images: []
      };

      const previousEventId = row.previousEventId;
      getPreviousEvent(previousEventId).then(response => {
        if (response && response.data) {
          this.previewForm = response.data;
          this.openPreview = true;
        } else {
          this.$modal.msgError(response.msg || "获取历史活动信息失败");
        }
      }).catch(error => {
        console.error("获取历史活动信息失败:", error);
      });
    },
    /** 上传图片按钮操作 */
    handleImageUpload() {
      this.uploadForm = {
        file: null,
        previousEventId: this.previewForm.previousEventId,
        previousEventTitle: this.previewForm.previousEventTitle,
        previousEventDescription: ''
      };
      this.fileList = [];
      this.openUpload = true;
    },
    /** 取消上传 */
    cancelUpload() {
      this.openUpload = false;
      this.fileList = [];
      this.uploadForm.file = null;
      this.uploadForm.previousEventDescription = '';
    },
    /** 处理图片文件上传 */
    handleImageFileUpload(options) {
      this.uploadForm.file = options.file;
      this.fileList = [{ name: options.file.name, url: URL.createObjectURL(options.file) }];
    },
    /** 提交上传表单 */
    submitUploadForm() {
      if (!this.uploadForm.file) {
        this.$message.error('请选择要上传的图片!');
        return;
      }

      this.uploadLoading = true;
      uploadPreviousEventImage(
        this.uploadForm.file,
        this.uploadForm.previousEventDescription,
        this.uploadForm.previousEventId
      ).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess("上传成功");
          this.openUpload = false;
          // 刷新预览数据
          this.handlePreview({ previousEventId: this.previewForm.previousEventId });
        } else {
          this.$modal.msgError(response.msg || "上传失败");
        }
        this.uploadLoading = false;
      }).catch(error => {
        console.error("上传图片失败:", error);
        this.$modal.msgError("上传失败，请重试");
        this.uploadLoading = false;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.updateForm = JSON.parse(JSON.stringify(row));
      this.openUpdate = true;
    },

    /** 取消修改 */
    cancelUpdate() {
      this.openUpdate = false;
      this.updateForm = {
        previousEventId: undefined,
        previousEventTitle: undefined,
        previousEventIsOnline: true,
        previousEventTime: undefined,
        previousEventDescription: undefined,
        previousEventLikesCount: 0,
        previousEventTag: undefined,
        previousEventLocation: undefined,
        previousEventIsShown: true,
        previousEventSort: 1,
        previousEventLikesOffset: 0
      };
    },

    /** 提交修改表单 */
    submitUpdateForm() {
      this.$refs["updateForm"].validate(valid => {
        if (valid) {
          this.updateLoading = true;
          updatePreviousEvent(this.updateForm).then(response => {
            if (response.code === 200) {
              this.$modal.msgSuccess("修改成功");
              this.openUpdate = false;
              this.getList();
            } else {
              this.$modal.msgError(response.msg || "修改失败");
            }
            this.updateLoading = false;
          }).catch(error => {
            console.error("修改历史活动失败:", error);
            this.$modal.msgError("修改失败，请重试");
            this.updateLoading = false;
          });
        }
      });
    },

    /** 替换头像按钮操作 */
    handleReplaceAvatar() {
      this.avatarForm = {
        previousEventId: this.previewForm.previousEventId,
        previousEventTitle: this.previewForm.previousEventTitle,
        currentAvatar: this.previewForm.previousEventAvatar,
        file: null
      };
      this.avatarFileList = [];
      this.openReplaceAvatar = true;
    },

    /** 头像上传前检查 */
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG && !isPNG) {
        this.$message.error('上传头像只能是 JPG 或 PNG 格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('上传头像大小不能超过 2MB!');
        return false;
      }

      this.avatarForm.file = file;
      this.avatarFileList = [{
        name: file.name,
        url: URL.createObjectURL(file),
        raw: file
      }];

      return true;
    },

    /** 头像文件数量超出限制时的钩子 */
    handleAvatarExceed() {
      this.$message.error('最多只能上传1张头像');
    },

    /** 头像自定义上传请求 */
    handleAvatarHttpRequest() {
      // 不自动上传，等用户点击确定按钮时再上传
    },

    /** 头像文件变更 */
    handleAvatarFileChange(file) {
      if (file && file.raw) {
        this.avatarForm.file = file.raw;
      }
    },

    /** 取消头像上传 */
    cancelAvatarUpload() {
      this.openReplaceAvatar = false;
      this.avatarFileList = [];
      this.avatarForm = {
        previousEventId: undefined,
        previousEventTitle: '',
        currentAvatar: '',
        file: null
      };
    },

    /** 提交头像上传 */
    submitAvatarUpload() {
      if (!this.avatarForm.file) {
        this.$message.error('请选择要上传的头像');
        return;
      }

      this.avatarLoading = true;
      uploadPreviousEventAvatar(this.avatarForm.file, this.avatarForm.previousEventId).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess("头像替换成功");
          this.openReplaceAvatar = false;
          // 刷新预览数据
          this.handlePreview({ previousEventId: this.previewForm.previousEventId });
        } else {
          this.$modal.msgError(response.msg || "头像替换失败");
        }
        this.avatarLoading = false;
      }).catch(error => {
        console.error("头像替换失败:", error);
        this.$modal.msgError("头像替换失败，请重试");
        this.avatarLoading = false;
      });
    },

    /** 格式化日期时间 */
    formatDateTime(dateTime) {
      if (!dateTime) {
        return '';
      }
      const date = new Date(dateTime);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    }
  }
};
</script>

<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.empty-block {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}
</style>
