import axios from 'axios'
import { parseStrEmpty } from "@/utils/ruoyi";
import { getToken } from '@/utils/auth'

// 创建axios实例
const miniappRequest = axios.create({
  // 小程序API的基础URL
  baseURL: 'http://127.0.0.1:9001/app/java',
  // 超时
  timeout: 10000
})

// request拦截器
miniappRequest.interceptors.request.use(config => {
  // 添加token
  if (getToken()) {
    config.headers['Authorization'] = 'Bearer ' + getToken()
  }
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})

// 响应拦截器
miniappRequest.interceptors.response.use(
  response => {
    // 直接返回数据
    return response.data
  },
  error => {
    console.log('err' + error)
    return Promise.reject(error)
  }
)

// 查询小程序用户列表
export function listUser(query) {
  return miniappRequest({
    url: '/miniapp/user/all',
    method: 'get',
    params: query
  })
}

// 查询小程序用户详细
export function getUser(userId) {
  return miniappRequest({
    url: '/miniapp/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}

// 新增小程序用户
export function addUser(data) {
  return miniappRequest({
    url: '/miniapp/user',
    method: 'post',
    data: data
  })
}

// 修改小程序用户
export function updateUser(data) {
  return miniappRequest({
    url: '/miniapp/user',
    method: 'put',
    data: data
  })
}

// 删除小程序用户
export function delUser(userId) {
  return miniappRequest({
    url: '/miniapp/user/' + userId,
    method: 'delete'
  })
}

// 根据ID查询小程序用户详细信息（包含角色信息）
export function getUserById(userId) {
  return miniappRequest({
    url: '/miniapp/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}

// 修改用户积分
export function updateUserPoint(userId, userPoint) {
  return miniappRequest({
    url: '/miniapp/user/point',
    method: 'post',
    params: {
      userId,
      userPoint
    }
  })
}

// 设置用户为VIP
export function updateUserVip(userId) {
  return miniappRequest({
    url: '/miniapp/user/vip',
    method: 'post',
    params: {
      userId
    }
  })
}

// 修改用户角色经验值
export function updateUserRole(userId, roleId, experiencePoint) {
  return miniappRequest({
    url: '/miniapp/user/role',
    method: 'post',
    params: {
      userId,
      roleId,
      experiencePoint
    }
  })
}
