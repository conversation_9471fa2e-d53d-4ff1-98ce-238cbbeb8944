import axios from 'axios'
import { parseStrEmpty } from "@/utils/ruoyi";
import { getToken } from '@/utils/auth'

// 创建axios实例
const miniappRequest = axios.create({
  // 小程序API的基础URL
  baseURL: 'http://127.0.0.1:9001/app/java',
  // 超时
  timeout: 10000
})

// request拦截器
miniappRequest.interceptors.request.use(config => {
  // 添加token
  if (getToken()) {
    config.headers['Authorization'] = 'Bearer ' + getToken()
  }
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})

// 响应拦截器
miniappRequest.interceptors.response.use(
  response => {
    // 直接返回数据
    return response.data
  },
  error => {
    console.log('err' + error)
    return Promise.reject(error)
  }
)

// 查询活动列表
export function listActivity(query) {
  return miniappRequest({
    url: '/miniapp/event/all',
    method: 'get',
    params: query
  })
}

// 查询活动详细
export function getActivity(eventId) {
  return miniappRequest({
    url: '/miniapp/event/' + parseStrEmpty(eventId),
    method: 'get'
  })
}

// 新增活动
export function addActivity(data) {
  return miniappRequest({
    url: '/miniapp/event',
    method: 'post',
    data: data
  })
}

// 修改活动
export function updateActivity(data) {
  return miniappRequest({
    url: '/miniapp/event',
    method: 'put',
    data: data
  })
}

// 删除活动
export function delActivity(eventId) {
  return miniappRequest({
    url: '/miniapp/event/' + eventId,
    method: 'delete'
  })
}

// 上传活动图片
export function uploadEventImage(file, eventId, imageDescription, isMain) {
  const formData = new FormData();
  formData.append('file', file);

  // 将DTO的字段作为单独的表单字段添加
  formData.append('eventId', eventId);
  formData.append('description', imageDescription);
  formData.append('isMain', isMain ? 1 : 0);

  return miniappRequest({
    url: '/miniapp/event/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
