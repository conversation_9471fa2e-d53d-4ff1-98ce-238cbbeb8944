import axios from 'axios'
import { parseStrEmpty } from "@/utils/ruoyi";
import { getToken } from '@/utils/auth'

// 创建axios实例
const miniappRequest = axios.create({
  // 小程序API的基础URL
  baseURL: 'http://127.0.0.1:9001/app/java',
  // 超时
  timeout: 10000
})

// request拦截器
miniappRequest.interceptors.request.use(config => {
  // 添加token
  if (getToken()) {
    config.headers['Authorization'] = 'Bearer ' + getToken()
  }
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})

// 响应拦截器
miniappRequest.interceptors.response.use(
  response => {
    // 直接返回数据
    return response.data
  },
  error => {
    console.log('err' + error)
    return Promise.reject(error)
  }
)

// 查询公司列表
export function listCompany(query) {
  return miniappRequest({
    url: '/miniapp/company/all',
    method: 'get',
    params: query
  })
}

// 查询公司详细
export function getCompany(companyId) {
  return miniappRequest({
    url: '/miniapp/company/' + parseStrEmpty(companyId),
    method: 'get'
  })
}

// 新增公司
export function addCompany(file, companyName, companySlogan, companyDetail, companyIsCooperated, companyLikesCount, companyLikesOffset, companySort) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('companyName', companyName);
  formData.append('companySlogan', companySlogan);
  formData.append('companyDetail', companyDetail);
  formData.append('companyIsCooperated', companyIsCooperated);
  formData.append('companyLikesCount', companyLikesCount);
  formData.append('companyLikesOffset', companyLikesOffset || 0);
  formData.append('companySort', companySort || 0);

  return miniappRequest({
    url: '/miniapp/company',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 修改公司
export function updateCompany(data) {
  return miniappRequest({
    url: '/miniapp/company',
    method: 'put',
    data: data
  })
}

// 删除公司
export function deleteCompany(companyId) {
  return miniappRequest({
    url: '/miniapp/company/delete',
    method: 'get',
    params: { companyId }
  })
}

// 上传公司图片
export function uploadCompanyImage(file, description, companyId) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('description', description);
  formData.append('companyId', companyId);

  return miniappRequest({
    url: '/miniapp/company/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除公司图片
export function deleteCompanyImage(companyImageId) {
  return miniappRequest({
    url: '/miniapp/company/image/delete',
    method: 'get',
    params: { companyImageId }
  })
}

// 添加公司职位
export function addCompanyJob(data) {
  return miniappRequest({
    url: '/miniapp/company/job',
    method: 'post',
    data: data
  })
}

// 修改公司职位
export function updateCompanyJob(data) {
  return miniappRequest({
    url: '/miniapp/company/job',
    method: 'put',
    data: data
  })
}

// 删除公司职位
export function deleteCompanyJob(jobId) {
  return miniappRequest({
    url: '/miniapp/company/job/delete',
    method: 'get',
    params: { jobId }
  })
}

// 获取职位申请人列表
export function getJobApplicants(jobId) {
  return miniappRequest({
    url: '/miniapp/company/user/' + jobId,
    method: 'get'
  })
}

// 替换公司头像
export function uploadCompanyAvatar(file, companyId) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('companyId', companyId);

  return miniappRequest({
    url: '/miniapp/company/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
