<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--公司数据-->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>公司管理</span>
          </div>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['miniapp:company:add']"
              >新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['miniapp:company:edit']"
              >修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['miniapp:company:remove']"
              >删除</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>

          <el-table v-loading="loading" :data="companyList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="公司ID" align="center" prop="companyId" width="120" />
            <el-table-column label="公司名称" align="center" prop="companyName" />
            <el-table-column label="公司口号" align="center" prop="companySlogan" :show-overflow-tooltip="true" />
            <el-table-column label="公司详情" align="center" prop="companyDetail" :show-overflow-tooltip="true" />
            <el-table-column label="公司Logo" align="center" width="100">
              <template slot-scope="scope">
                <el-image
                  style="width: 60px; height: 60px;"
                  :src="getLogoUrl(scope.row.companyAvatar)"
                  :preview-src-list="[getLogoUrl(scope.row.companyAvatar)]"
                  fit="cover">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="合作状态" align="center" prop="companyIsCooperated">
              <template slot-scope="scope">
                <el-tag :type="scope.row.companyIsCooperated === 1 ? 'success' : 'info'">
                  {{ scope.row.companyIsCooperated === 1 ? '已合作' : '未合作' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="点赞数" align="center" prop="companyLikesCount" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handlePreview(scope.row)"
                >预览</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['miniapp:company:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['miniapp:company:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改公司对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="公司名称" prop="companyName">
              <el-input v-model="form.companyName" placeholder="请输入公司名称" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="公司口号" prop="companySlogan">
              <el-input v-model="form.companySlogan" placeholder="请输入公司口号" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="公司详情" prop="companyDetail">
              <el-input v-model="form.companyDetail" type="textarea" placeholder="请输入公司详情" maxlength="500" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="公司Logo" prop="companyAvatar">
              <!-- 新增时显示上传组件 -->
              <el-upload
                v-if="form.companyId === undefined"
                class="avatar-uploader"
                action="#"
                :http-request="handleFormHttpRequest"
                :before-upload="handleFormBeforeUpload"
                :limit="1"
                :on-exceed="handleFormExceed"
                :file-list="formFileList"
                :on-change="handleFormFileChange"
                :auto-upload="false"
                list-type="picture-card">
                <i class="el-icon-plus"></i>
                <div slot="tip" class="el-upload__tip">
                  <span style="color: #F56C6C; font-weight: bold;">*必选项</span> - 只能上传jpg/png文件，且不超过5MB
                </div>
              </el-upload>
              <!-- 显示图片预览 -->
              <el-image
                v-if="form.companyAvatar"
                style="width: 100px; height: 100px; margin-top: 10px;"
                :src="getLogoUrl(form.companyAvatar)"
                fit="cover">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <!-- 修改时显示提示信息 -->
              <div v-if="form.companyId !== undefined" class="logo-info">
                <!-- 显示图片URL -->
                <el-form-item label="Logo URL" prop="companyAvatar">
                  <el-input v-model="form.companyAvatar" placeholder="Logo URL" />
                </el-form-item>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="合作状态" prop="companyIsCooperated">
              <el-radio-group v-model="form.companyIsCooperated">
                <el-radio :label="1">已合作</el-radio>
                <el-radio :label="0">未合作</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="点赞数" prop="companyLikesCount">
              <el-input-number v-model="form.companyLikesCount" :min="0" :max="10000" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="点赞偏移量" prop="companyLikesOffset">
              <el-input-number v-model="form.companyLikesOffset" :min="0" :max="10000" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="companySort">
              <el-input-number v-model="form.companySort" :min="0" :max="1000" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="formLoading">确 定</el-button>
        <el-button @click="cancel" :disabled="formLoading">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 公司详情预览对话框 -->
    <el-dialog :title="'公司详情 - ' + previewForm.companyName" :visible.sync="openPreview" width="800px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="公司ID">{{ previewForm.companyId }}</el-descriptions-item>
        <el-descriptions-item label="公司名称">{{ previewForm.companyName }}</el-descriptions-item>
        <el-descriptions-item label="公司口号">{{ previewForm.companySlogan }}</el-descriptions-item>
        <el-descriptions-item label="公司详情">{{ previewForm.companyDetail }}</el-descriptions-item>
        <el-descriptions-item label="合作状态">
          <el-tag :type="previewForm.companyIsCooperated === 1 ? 'success' : 'info'">
            {{ previewForm.companyIsCooperated === 1 ? '已合作' : '未合作' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="点赞数">{{ previewForm.companyLikesCount }}</el-descriptions-item>
        <el-descriptions-item label="点赞偏移量">{{ previewForm.companyLikesOffset }}</el-descriptions-item>
        <el-descriptions-item label="排序">{{ previewForm.companySort }}</el-descriptions-item>
        <el-descriptions-item label="公司Logo">
          <el-image
            style="width: 100px; height: 100px;"
            :src="getLogoUrl(previewForm.companyAvatar)"
            :preview-src-list="[getLogoUrl(previewForm.companyAvatar)]"
            fit="cover">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <div style="margin-top: 10px;">
            <span style="word-break: break-all;">{{ previewForm.companyAvatar }}</span>
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 公司图片 -->
      <div class="company-images" style="margin-top: 20px;">
        <div class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <h3 style="margin: 0;">公司图片</h3>
          <el-button type="primary" size="mini" icon="el-icon-upload2" @click="handleImageUpload">上传图片</el-button>
        </div>
        <el-empty v-if="!previewForm.images || previewForm.images.length === 0" description="暂无图片"></el-empty>
        <el-row :gutter="10" v-else>
          <el-col :span="8" v-for="image in previewForm.images" :key="'image-' + image.companyImageId">
            <el-card :body-style="{ padding: '0px' }">
              <el-image
                style="width: 100%; height: 150px;"
                :src="getLogoUrl(image.companyImageUrl)"
                :preview-src-list="previewForm.images.map(img => getLogoUrl(img.companyImageUrl))"
                fit="cover">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <div style="padding: 10px;">
                <div style="margin-bottom: 5px;">{{ image.description }}</div>
                <el-button type="danger" size="mini" icon="el-icon-delete" @click="handleDeleteImage(image)">删除</el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 公司职位 -->
      <div class="company-jobs" style="margin-top: 20px;">
        <div class="section-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <h3 style="margin: 0;">公司职位</h3>
          <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAddJob">添加职位</el-button>
        </div>
        <el-empty v-if="!previewForm.jobs || previewForm.jobs.length === 0" description="暂无职位"></el-empty>
        <el-table v-else :data="previewForm.jobs" style="width: 100%">
          <el-table-column prop="jobTitle" label="职位名称" width="150"></el-table-column>
          <el-table-column prop="jobDescription" label="职位描述" show-overflow-tooltip></el-table-column>
          <el-table-column prop="location" label="工作地点" width="100"></el-table-column>
          <el-table-column prop="salary" label="薪资" width="120"></el-table-column>
          <el-table-column prop="tag1" label="标签1" width="100"></el-table-column>
          <el-table-column prop="tag2" label="标签2" width="100"></el-table-column>
          <el-table-column prop="isActive" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="scope.row.isActive === 1 ? 'success' : 'info'">
                {{ scope.row.isActive === 1 ? '招聘中' : '已关闭' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="mini" icon="el-icon-edit" @click="handleEditJob(scope.row)">修改</el-button>
              <el-button type="text" size="mini" icon="el-icon-delete" @click="handleDeleteJob(scope.row)">删除</el-button>
              <el-button type="text" size="mini" icon="el-icon-user" @click="handleViewApplicants(scope.row)">查看报名人</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 上传图片对话框 -->
    <el-dialog title="上传公司图片" :visible.sync="openUpload" width="500px" append-to-body>
      <el-form ref="uploadForm" :model="uploadForm" label-width="100px">
        <el-form-item label="选择图片" prop="file">
          <el-upload
            class="upload-demo"
            action="#"
            :http-request="handleHttpRequest"
            :before-upload="beforeUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="uploadFileList"
            :on-change="handleFileChange"
            :auto-upload="false"
            list-type="picture">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过5MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="图片描述" prop="imageDescription">
          <el-input v-model="uploadForm.imageDescription" type="textarea" placeholder="请输入图片描述"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpload" :loading="uploadLoading">确 定</el-button>
        <el-button @click="cancelUpload" :disabled="uploadLoading">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 职位表单对话框 -->
    <el-dialog :title="jobTitle" :visible.sync="openJob" width="600px" append-to-body>
      <el-form ref="jobForm" :model="jobForm" :rules="jobRules" label-width="100px">
        <el-form-item label="职位名称" prop="jobTitle">
          <el-input v-model="jobForm.jobTitle" placeholder="请输入职位名称"></el-input>
        </el-form-item>
        <el-form-item label="职位描述" prop="jobDescription">
          <el-input v-model="jobForm.jobDescription" type="textarea" placeholder="请输入职位描述"></el-input>
        </el-form-item>
        <el-form-item label="工作地点" prop="location">
          <el-input v-model="jobForm.location" placeholder="请输入工作地点"></el-input>
        </el-form-item>
        <el-form-item label="薪资" prop="salary">
          <el-input v-model="jobForm.salary" placeholder="请输入薪资范围，如：15k-25k/月"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-radio-group v-model="jobForm.isActive">
            <el-radio :label="1">招聘中</el-radio>
            <el-radio :label="0">已关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="标签1" prop="tag1">
          <el-input v-model="jobForm.tag1" placeholder="请输入标签1"></el-input>
        </el-form-item>
        <el-form-item label="标签2" prop="tag2">
          <el-input v-model="jobForm.tag2" placeholder="请输入标签2"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitJob" :loading="jobLoading">确 定</el-button>
        <el-button @click="cancelJob" :disabled="jobLoading">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 职位申请人对话框 -->
    <el-dialog :title="'申请人列表 - ' + currentJob.jobTitle" :visible.sync="openApplicants" width="800px" append-to-body>
      <div v-loading="applicantsLoading">
        <el-empty v-if="!applicantsList || applicantsList.length === 0" description="暂无申请人"></el-empty>
        <el-table v-else :data="applicantsList" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="userId" label="用户ID" width="220" show-overflow-tooltip></el-table-column>
          <el-table-column prop="status" label="状态" width="120">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="appliedAt" label="申请时间" width="180">
            <template slot-scope="scope">
              {{ formatDateTime(scope.row.appliedAt) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="openApplicants = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCompany,
  getCompany,
  addCompany,
  updateCompany,
  uploadCompanyImage,
  addCompanyJob,
  updateCompanyJob,
  getJobApplicants,
  deleteCompany,
  deleteCompanyImage,
  deleteCompanyJob
} from "@/api/miniapp/brand";
import RightToolbar from "@/components/RightToolbar";
import Pagination from "@/components/Pagination";

export default {
  name: "MiniappCompany",
  components: {
    RightToolbar,
    Pagination
  },
  data() {
    return {
      // OSS基础URL
      ossBaseUrl: "https://xiuzhe-weixinapp.oss-cn-shanghai.aliyuncs.com/",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公司表格数据
      companyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {
        companyId: undefined,
        companyName: undefined,
        companySlogan: undefined,
        companyDetail: undefined,
        companyAvatar: undefined,
        companyIsCooperated: 0,
        companyLikesCount: 0,
        companyLikesOffset: 0,
        companySort: 0,
        file: null
      },
      // 表单文件列表
      formFileList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyLikesOffset: undefined,
        companySort: undefined
      },
      // 表单校验
      rules: {
        companyName: [
          { required: true, message: "公司名称不能为空", trigger: "blur" }
        ],
        companySlogan: [
          { required: true, message: "公司口号不能为空", trigger: "blur" }
        ],
        companyDetail: [
          { required: true, message: "公司详情不能为空", trigger: "blur" }
        ]
      },
      // 预览对话框
      openPreview: false,
      // 预览表单数据
      previewForm: {
        companyId: undefined,
        companyName: undefined,
        companySlogan: undefined,
        companyDetail: undefined,
        companyAvatar: undefined,
        companyIsCooperated: 0,
        companyLikesCount: 0,
        companyLikesOffset: 0,
        companySort: 0,
        images: [],
        jobs: []
      },
      // 上传图片对话框
      openUpload: false,
      // 上传加载状态
      uploadLoading: false,
      // 表单提交加载状态
      formLoading: false,
      // 上传文件列表
      uploadFileList: [],
      // 上传表单
      uploadForm: {
        file: null,
        companyId: undefined,
        imageDescription: ''
      },
      // 职位对话框
      openJob: false,
      // 职位加载状态
      jobLoading: false,
      // 职位标题
      jobTitle: '',
      // 职位表单
      jobForm: {
        jobId: undefined,
        companyId: undefined,
        jobTitle: '',
        jobDescription: '',
        location: '',
        salary: '',
        isActive: 1,
        tag1: '',
        tag2: ''
      },
      // 职位表单校验
      jobRules: {
        jobTitle: [
          { required: true, message: "职位名称不能为空", trigger: "blur" }
        ],
        jobDescription: [
          { required: true, message: "职位描述不能为空", trigger: "blur" }
        ],
        location: [
          { required: true, message: "工作地点不能为空", trigger: "blur" }
        ],
        salary: [
          { required: true, message: "薪资不能为空", trigger: "blur" }
        ]
      },
      // 申请人对话框
      openApplicants: false,
      // 申请人加载状态
      applicantsLoading: false,
      // 当前查看的职位
      currentJob: {},
      // 申请人列表
      applicantsList: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 获取Logo完整URL */
    getLogoUrl(logo) {
      if (!logo) {
        return "";
      }
      // 如果已经是完整URL，直接返回
      if (logo.startsWith('http')) {
        return logo;
      }
      // 否则拼接OSS基础URL
      return this.ossBaseUrl + logo;
    },
    /** 查询公司列表 */
    getList() {
      this.loading = true;
      listCompany(this.queryParams).then(response => {
        // 根据实际API返回格式调整
        if (response.data) {
          this.companyList = response.data;
          this.total = response.data.length;
        } else {
          this.companyList = [];
          this.total = 0;
        }
        this.loading = false;
      }).catch(error => {
        console.error("获取公司列表失败:", error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        companyId: undefined,
        companyName: undefined,
        companySlogan: undefined,
        companyDetail: undefined,
        companyAvatar: undefined,
        companyIsCooperated: 0,
        companyLikesCount: 0,
        companyLikesOffset: 0,
        companySort: 0,
        file: null
      };
      // 确保清空文件列表
      this.formFileList = [];
      // 重置表单验证
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.companyId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加公司";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const companyId = row.companyId || this.ids[0];
      getCompany(companyId).then(response => {
        if (response && response.data) {
          this.form = response.data;
          // 清空文件相关字段，因为修改时不允许上传图片
          this.form.file = null;
          this.formFileList = [];
          this.open = true;
          this.title = "修改公司";
        } else {
          this.$modal.msgError(response.msg || "获取公司信息失败");
        }
      }).catch(error => {
        console.error("获取公司信息失败:", error);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.formLoading = true;
          if (this.form.companyId != undefined) {
            // 修改公司信息 - 不包含图片上传
            // 创建一个不包含文件字段的对象
            const updateData = {
              companyId: this.form.companyId,
              companyName: this.form.companyName,
              companySlogan: this.form.companySlogan,
              companyDetail: this.form.companyDetail,
              companyIsCooperated: this.form.companyIsCooperated,
              companyLikesCount: this.form.companyLikesCount,
              companyLikesOffset: this.form.companyLikesOffset,
              companySort: this.form.companySort,
              companyAvatar: this.form.companyAvatar // 保留原有的头像路径
            };

            updateCompany(updateData).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              } else {
                this.$modal.msgError(response.msg || "修改失败");
              }
              this.formLoading = false;
            }).catch(error => {
              console.error("修改公司失败:", error);
              this.$modal.msgError("修改失败，请重试");
              this.formLoading = false;
            });
          } else {
            // 新增公司信息 - 需要上传图片
            // 从文件列表或表单中获取文件对象
            let fileToUpload = this.form.file;

            // 如果form.file为空但文件列表不为空，则从文件列表中获取文件
            if (!fileToUpload && this.formFileList.length > 0 && this.formFileList[0].raw) {
              fileToUpload = this.formFileList[0].raw;
              // 更新form.file以确保一致性
              this.form.file = fileToUpload;
            }

            if (!fileToUpload) {
              this.$message.error('文件上传出错，请重新选择文件');
              this.formLoading = false;
              return;
            }

            console.log('准备上传文件:', fileToUpload.name);

            // 使用新的API函数，传递文件和其他参数
            addCompany(
              fileToUpload,
              this.form.companyName,
              this.form.companySlogan,
              this.form.companyDetail,
              this.form.companyIsCooperated,
              this.form.companyLikesCount || 0,
              this.form.companyLikesOffset || 0,
              this.form.companySort || 0
            ).then(response => {
              if (response.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              } else {
                this.$modal.msgError(response.msg || "新增失败");
              }
              this.formLoading = false;
            }).catch(error => {
              console.error("新增公司失败:", error);
              this.$modal.msgError("新增失败，请重试");
              this.formLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const companyIds = row.companyId || this.ids;
      this.$modal.confirm('是否确认删除公司编号为"' + companyIds + '"的数据项?').then(() => {
        return deleteCompany(companyIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 预览按钮操作 */
    handlePreview(row) {
      this.previewForm = {
        companyId: undefined,
        companyName: undefined,
        companySlogan: undefined,
        companyDetail: undefined,
        companyAvatar: undefined,
        companyIsCooperated: 0,
        companyLikesCount: 0,
        images: [],
        jobs: []
      };

      const companyId = row.companyId;
      getCompany(companyId).then(response => {
        if (response && response.data) {
          this.previewForm = response.data;
          this.openPreview = true;
        } else {
          this.$modal.msgError(response.msg || "获取公司信息失败");
        }
      }).catch(error => {
        console.error("获取公司信息失败:", error);
      });
    },

    /** 上传图片按钮操作 */
    handleImageUpload() {
      this.uploadForm = {
        file: null,
        companyId: this.previewForm.companyId,
        imageDescription: ''
      };
      this.uploadFileList = [];
      this.openUpload = true;
    },

    /** 上传前检查 */
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
        return false;
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!');
        return false;
      }

      // 保存文件到表单数据
      this.uploadForm.file = file;

      // 更新文件列表，使上传组件显示已选择的文件
      this.uploadFileList = [{
        name: file.name,
        url: URL.createObjectURL(file),
        raw: file
      }];

      console.log('公司图片已通过检查:', file.name);
      return true; // 允许文件显示在上传列表中
    },

    /** 文件数量超出限制时的钩子 */
    handleExceed() {
      this.$message.error('最多只能上传1张图片');
    },

    /** 自定义上传请求 */
    handleHttpRequest() {
      // 这个方法不会被调用，因为我们设置了 auto-upload 为 false
      // 但是需要提供这个方法以避免 el-upload 报错
    },

    /** 处理文件变更 */
    handleFileChange(file) {
      if (file && file.raw) {
        // 确保文件被保存到表单中
        this.uploadForm.file = file.raw;

        // 更新文件列表，使上传组件显示已选择的文件
        this.uploadFileList = [{
          name: file.name,
          url: URL.createObjectURL(file.raw),
          raw: file.raw
        }];

        console.log('公司图片已选择:', file.name);
      }
    },

    /** 表单自定义上传请求 */
    handleFormHttpRequest() {
      // 这个方法不会被调用，因为我们设置了 auto-upload 为 false
      // 但是需要提供这个方法以避免 el-upload 报错
    },

    /** 表单上传前检查 */
    handleFormBeforeUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
        return false;
      }
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!');
        return false;
      }

      // 保存文件到表单数据
      this.form.file = file;

      // 更新文件列表，使上传组件显示已选择的文件
      this.formFileList = [{
        name: file.name,
        url: URL.createObjectURL(file),
        raw: file // 保存原始文件对象
      }];

      console.log('文件已选择:', file.name);
      return false; // 阻止自动上传
    },

    /** 表单文件数量超出限制时的钩子 */
    handleFormExceed() {
      this.$message.error('最多只能上传1张图片');
    },

    /** 表单文件变更时的钩子 */
    handleFormFileChange(file) {
      if (file && file.raw) {
        // 确保文件被保存到表单中
        this.form.file = file.raw;
        console.log('文件已更新:', file.name);
      }
    },

    /** 取消上传 */
    cancelUpload() {
      this.openUpload = false;
      this.uploadForm = {
        file: null,
        companyId: undefined,
        imageDescription: ''
      };
      this.uploadFileList = [];
    },

    /** 提交上传 */
    submitUpload() {
      // 从文件列表或表单中获取文件对象
      let fileToUpload = this.uploadForm.file;

      // 如果uploadForm.file为空但文件列表不为空，则从文件列表中获取文件
      if (!fileToUpload && this.uploadFileList.length > 0 && this.uploadFileList[0].raw) {
        fileToUpload = this.uploadFileList[0].raw;
        // 更新uploadForm.file以确保一致性
        this.uploadForm.file = fileToUpload;
      }

      if (!fileToUpload) {
        this.$message.error('请选择要上传的图片');
        return;
      }

      console.log('准备上传公司图片:', fileToUpload.name);

      this.uploadLoading = true;
      uploadCompanyImage(
        fileToUpload,
        this.uploadForm.imageDescription,
        this.uploadForm.companyId
      ).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess("上传成功");
          this.openUpload = false;
          // 刷新预览
          this.handlePreview({ companyId: this.previewForm.companyId });
        } else {
          this.$modal.msgError(response.msg || "上传失败");
        }
        this.uploadLoading = false;
      }).catch(error => {
        console.error("上传图片失败:", error);
        this.$modal.msgError("上传失败，请重试");
        this.uploadLoading = false;
      });
    },

    /** 删除图片 */
    handleDeleteImage(image) {
      this.$modal.confirm('是否确认删除该图片?').then(() => {
        return deleteCompanyImage(image.companyImageId);
      }).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess("删除成功");
          // 刷新预览
          this.handlePreview({ companyId: this.previewForm.companyId });
        } else {
          this.$modal.msgError(response.msg || "删除失败");
        }
      }).catch(() => {});
    },

    /** 添加职位按钮操作 */
    handleAddJob() {
      this.jobForm = {
        jobId: undefined,
        companyId: this.previewForm.companyId,
        jobTitle: '',
        jobDescription: '',
        location: '',
        salary: '',
        isActive: 1,
        tag1: '',
        tag2: ''
      };
      this.jobTitle = "添加职位";
      this.openJob = true;
    },

    /** 编辑职位按钮操作 */
    handleEditJob(job) {
      this.jobForm = JSON.parse(JSON.stringify(job)); // 深拷贝
      this.jobTitle = "修改职位";
      this.openJob = true;
    },

    /** 取消职位操作 */
    cancelJob() {
      this.openJob = false;
      this.jobForm = {
        jobId: undefined,
        companyId: undefined,
        jobTitle: '',
        jobDescription: '',
        location: '',
        salary: '',
        isActive: 1,
        tag1: '',
        tag2: ''
      };
    },

    /** 提交职位表单 */
    submitJob() {
      this.$refs["jobForm"].validate(valid => {
        if (valid) {
          this.jobLoading = true;
          if (this.jobForm.jobId != undefined) {
            updateCompanyJob(this.jobForm).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.openJob = false;
              // 刷新预览
              this.handlePreview({ companyId: this.previewForm.companyId });
              this.jobLoading = false;
            }).catch(error => {
              console.error("修改职位失败:", error);
              this.$modal.msgError("修改失败，请重试");
              this.jobLoading = false;
            });
          } else {
            addCompanyJob(this.jobForm).then(() => {
              this.$modal.msgSuccess("添加成功");
              this.openJob = false;
              // 刷新预览
              this.handlePreview({ companyId: this.previewForm.companyId });
              this.jobLoading = false;
            }).catch(error => {
              console.error("添加职位失败:", error);
              this.$modal.msgError("添加失败，请重试");
              this.jobLoading = false;
            });
          }
        }
      });
    },

    /** 删除职位 */
    handleDeleteJob(job) {
      this.$modal.confirm('是否确认删除该职位?').then(() => {
        return deleteCompanyJob(job.jobId);
      }).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess("删除成功");
          // 刷新预览
          this.handlePreview({ companyId: this.previewForm.companyId });
        } else {
          this.$modal.msgError(response.msg || "删除失败");
        }
      }).catch(() => {});
    },

    /** 查看职位申请人 */
    handleViewApplicants(job) {
      this.currentJob = job;
      this.applicantsList = [];
      this.openApplicants = true;
      this.applicantsLoading = true;

      getJobApplicants(job.jobId).then(response => {
        if (response && response.data) {
          this.applicantsList = response.data;
        } else {
          this.applicantsList = [];
        }
        this.applicantsLoading = false;
      }).catch(error => {
        console.error("获取职位申请人失败:", error);
        this.$modal.msgError("获取申请人列表失败");
        this.applicantsLoading = false;
      });
    },

    /** 获取申请状态类型 */
    getStatusType(status) {
      switch (status) {
        case 'pending':
          return 'warning';
        case 'approved':
          return 'success';
        case 'rejected':
          return 'danger';
        default:
          return 'info';
      }
    },

    /** 获取申请状态文本 */
    getStatusText(status) {
      switch (status) {
        case 'pending':
          return '待处理';
        case 'approved':
          return '已通过';
        case 'rejected':
          return '已拒绝';
        default:
          return '未知';
      }
    },

    /** 格式化日期时间 */
    formatDateTime(dateTime) {
      if (!dateTime) {
        return '';
      }
      const date = new Date(dateTime);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    }
  }
};
</script>

<style scoped>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.logo-info {
  margin-top: 10px;
}
.logo-tip {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
  margin: 5px 0;
}
</style>
