<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--活动数据-->
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>活动管理</span>
          </div>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAdd"
                v-hasPermi="['miniapp:activity:add']"
              >新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdate"
                v-hasPermi="['miniapp:activity:edit']"
              >修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['miniapp:activity:remove']"
              >删除</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>

          <el-table v-loading="loading" :data="activityList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="活动ID" align="center" prop="eventId" width="80" />
            <el-table-column label="活动标题" align="center" prop="eventTitle" />
            <el-table-column label="活动副标题" align="center" prop="eventSubTitle" :show-overflow-tooltip="true" />
            <el-table-column label="创建者" align="center" prop="eventCreatorName" width="100" />
            <el-table-column label="创建者头像" align="center" width="80">
              <template slot-scope="scope">
                <el-image
                  style="width: 50px; height: 50px; border-radius: 50%;"
                  :src="getAvatarUrl(scope.row.eventCreatorAvatar)"
                  :preview-src-list="[getAvatarUrl(scope.row.eventCreatorAvatar)]"
                  fit="cover">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="活动类别" align="center" prop="eventCategory" width="100" />
            <el-table-column label="积分消耗" align="center" prop="eventPointCost" width="80" />
            <el-table-column label="价格" align="center" prop="eventPriceCost" width="80" />
            <el-table-column label="线上/线下" align="center" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.isOnline === 1 ? 'primary' : 'success'">
                  {{ scope.row.isOnline === 1 ? '线上' : '线下' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="活动描述" align="center" prop="description" :show-overflow-tooltip="true" />
            <el-table-column label="开始时间" align="center" prop="startTime" width="150">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.startTime) }}
              </template>
            </el-table-column>
            <el-table-column label="结束时间" align="center" prop="endTime" width="150">
              <template slot-scope="scope">
                {{ formatDateTime(scope.row.endTime) }}
              </template>
            </el-table-column>
            <el-table-column label="地点" align="center" prop="location" width="120" />
            <el-table-column label="状态" align="center" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.isActive === 1 ? 'success' : 'info'">
                  {{ scope.row.isActive === 1 ? '进行中' : '已结束' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="点赞数" align="center" prop="likesCount" width="80" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-view"
                  @click="handleView(scope.row)"
                >查看</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:activity:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-picture"
                  @click="handleUploadImage(scope.row)"
                >上传图片</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:activity:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改活动对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="活动标题" prop="eventTitle">
              <el-input v-model="form.eventTitle" placeholder="请输入活动标题" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="活动副标题" prop="eventSubTitle">
              <el-input v-model="form.eventSubTitle" placeholder="请输入活动副标题" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建者" prop="eventCreatorName">
              <el-input v-model="form.eventCreatorName" placeholder="请输入创建者姓名" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建者头像" prop="eventCreatorAvatar">
              <el-input v-model="form.eventCreatorAvatar" placeholder="请输入创建者头像路径" maxlength="200" />
              <el-image
                style="width: 50px; height: 50px; border-radius: 50%; margin-top: 10px;"
                :src="getAvatarUrl(form.eventCreatorAvatar)"
                fit="cover">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="活动类别" prop="eventCategory">
              <el-select v-model="form.eventCategory" placeholder="请选择活动类别">
                <el-option label="创意挑战" value="创意挑战" />
                <el-option label="技术沙龙" value="技术沙龙" />
                <el-option label="学习交流" value="学习交流" />
                <el-option label="其他活动" value="其他活动" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="积分消耗" prop="eventPointCost">
              <el-input-number v-model="form.eventPointCost" :min="0" :max="1000" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="价格" prop="eventPriceCost">
              <el-input-number v-model="form.eventPriceCost" :min="0" :max="10000" :precision="2" :step="10" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="活动描述" prop="description">
              <el-input v-model="form.description" type="textarea" placeholder="请输入活动描述" maxlength="500" :rows="4" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="线上/线下" prop="isOnline">
              <el-radio-group v-model="form.isOnline">
                <el-radio :label="1">线上</el-radio>
                <el-radio :label="0">线下</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入活动地点" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="isActive">
              <el-radio-group v-model="form.isActive">
                <el-radio :label="1">进行中</el-radio>
                <el-radio :label="0">已结束</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="formLoading">确 定</el-button>
        <el-button @click="cancel" :disabled="formLoading">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看活动详情对话框 -->
    <el-dialog :title="'活动详情 - ' + detailForm.eventTitle" :visible.sync="openDetail" width="800px" append-to-body>
      <el-form label-width="120px" class="detail-form">
        <el-form-item label="活动ID:">
          <span>{{ detailForm.eventId }}</span>
        </el-form-item>
        <el-form-item label="活动标题:">
          <span>{{ detailForm.eventTitle }}</span>
        </el-form-item>
        <el-form-item label="活动副标题:">
          <span>{{ detailForm.eventSubTitle }}</span>
        </el-form-item>
        <el-form-item label="创建者:">
          <span>{{ detailForm.eventCreatorName }}</span>
        </el-form-item>
        <el-form-item label="创建者头像:">
          <el-image
            style="width: 80px; height: 80px; border-radius: 50%;"
            :src="getAvatarUrl(detailForm.eventCreatorAvatar)"
            :preview-src-list="[getAvatarUrl(detailForm.eventCreatorAvatar)]"
            fit="cover">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </el-form-item>
        <el-form-item label="活动类别:">
          <span>{{ detailForm.eventCategory }}</span>
        </el-form-item>
        <el-form-item label="积分消耗:">
          <span>{{ detailForm.eventPointCost }}</span>
        </el-form-item>
        <el-form-item label="价格:">
          <span>{{ detailForm.eventPriceCost }} 元</span>
        </el-form-item>
        <el-form-item label="线上/线下:">
          <el-tag :type="detailForm.isOnline === 1 ? 'primary' : 'success'">
            {{ detailForm.isOnline === 1 ? '线上' : '线下' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="活动描述:">
          <span>{{ detailForm.description }}</span>
        </el-form-item>
        <el-form-item label="开始时间:">
          <span>{{ formatDateTime(detailForm.startTime) }}</span>
        </el-form-item>
        <el-form-item label="结束时间:">
          <span>{{ formatDateTime(detailForm.endTime) }}</span>
        </el-form-item>
        <el-form-item label="地点:">
          <span>{{ detailForm.location }}</span>
        </el-form-item>
        <el-form-item label="状态:">
          <el-tag :type="detailForm.isActive === 1 ? 'success' : 'info'">
            {{ detailForm.isActive === 1 ? '进行中' : '已结束' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="点赞数:">
          <span>{{ detailForm.likesCount || 0 }}</span>
        </el-form-item>
        <el-form-item label="活动图片:" v-if="detailForm.images && detailForm.images.length > 0">
          <div class="image-list">
            <div v-for="(image, index) in detailForm.images" :key="index" class="image-item">
              <el-image
                style="width: 150px; height: 150px;"
                :src="getImageUrl(image.eventImageUrl)"
                :preview-src-list="detailForm.images.map(img => getImageUrl(img.eventImageUrl))"
                fit="cover">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <div class="image-desc">
                <span>{{ image.description }}</span>
                <el-tag v-if="image.isMain === 1" size="mini" type="success">主图</el-tag>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="openDetail = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 上传图片对话框 -->
    <el-dialog :title="'上传活动图片 - ' + uploadForm.eventTitle" :visible.sync="openUpload" width="500px" append-to-body>
      <el-form ref="uploadForm" :model="uploadForm" label-width="100px">
        <el-form-item label="活动ID">
          <span>{{ uploadForm.eventId }}</span>
        </el-form-item>
        <el-form-item label="活动标题">
          <span>{{ uploadForm.eventTitle }}</span>
        </el-form-item>
        <el-form-item label="图片" prop="file">
          <el-upload
            class="upload-demo"
            action="#"
            :http-request="handleFileUpload"
            :before-upload="beforeUpload"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
            list-type="picture">
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="图片描述" prop="imageDescription">
          <el-input v-model="uploadForm.imageDescription" placeholder="请输入图片描述" maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="是否主图" prop="isMain">
          <el-switch v-model="uploadForm.isMain" active-text="是" inactive-text="否"></el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitUpload" :loading="uploadLoading">确 定</el-button>
        <el-button @click="cancelUpload" :disabled="uploadLoading">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.detail-form .el-form-item {
  margin-bottom: 15px;
  border-bottom: 1px dashed #ebeef5;
  padding-bottom: 15px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.image-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-desc {
  margin-top: 5px;
  text-align: center;
  font-size: 12px;
  color: #606266;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-desc .el-tag {
  margin-top: 5px;
}
</style>

<script>
import { listActivity, getActivity, delActivity, addActivity, updateActivity, uploadEventImage } from "@/api/miniapp/activity";
import RightToolbar from "@/components/RightToolbar";
import Pagination from "@/components/Pagination";
import { addDateRange, parseTime } from "@/utils/ruoyi";

export default {
  name: "MiniappActivity",
  components: {
    RightToolbar,
    Pagination
  },
  data() {
    return {
      // OSS基础URL
      ossBaseUrl: "https://xiuzhe-weixinapp.oss-cn-shanghai.aliyuncs.com/",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 活动表格数据
      activityList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      openDetail: false,
      // 日期范围
      dateRange: [],
      // 详情表单
      detailForm: {
        eventId: undefined,
        eventTitle: "",
        eventSubTitle: "",
        eventCreatorName: "",
        eventCreatorAvatar: "",
        eventCategory: "",
        eventPointCost: 0,
        eventPriceCost: 0,
        isOnline: 1,
        description: "",
        startTime: "",
        endTime: "",
        location: "",
        isActive: 0,
        likesCount: 0,
        images: []
      },
      // 上传图片相关
      openUpload: false,
      uploadLoading: false,
      // 表单提交加载状态
      formLoading: false,
      fileList: [],
      uploadForm: {
        eventId: undefined,
        eventTitle: "",
        file: null,
        imageDescription: "",
        isMain: false
      },
      // 表单参数
      form: {
        eventId: undefined,
        eventTitle: undefined,
        eventSubTitle: undefined,
        eventCreatorName: undefined,
        eventCreatorAvatar: undefined,
        eventCategory: "创意挑战",
        eventPointCost: 0,
        eventPriceCost: 0,
        isOnline: 1,
        description: undefined,
        startTime: undefined,
        endTime: undefined,
        location: undefined,
        isActive: 1,
        likesCount: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单校验
      rules: {
        eventTitle: [
          { required: true, message: "活动标题不能为空", trigger: "blur" }
        ],
        eventCreatorName: [
          { required: true, message: "创建者姓名不能为空", trigger: "blur" }
        ],
        eventCategory: [
          { required: true, message: "活动类别不能为空", trigger: "blur" }
        ],
        description: [
          { required: true, message: "活动描述不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "blur" }
        ],
        location: [
          { required: true, message: "活动地点不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 获取头像完整URL */
    getAvatarUrl(avatar) {
      if (!avatar) {
        return "";
      }
      // 如果已经是完整URL，直接返回
      if (avatar.startsWith('http')) {
        return avatar;
      }
      // 否则拼接OSS基础URL
      return this.ossBaseUrl + avatar;
    },
    /** 获取图片完整URL */
    getImageUrl(imageUrl) {
      if (!imageUrl) {
        return "";
      }
      // 如果已经是完整URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl;
      }
      // 否则拼接OSS基础URL
      return this.ossBaseUrl + imageUrl;
    },
    /** 格式化日期时间 */
    formatDateTime(dateTime) {
      if (!dateTime) {
        return "";
      }
      // 使用parseTime函数格式化日期时间
      return parseTime(dateTime);
    },
    /** 查询活动列表 */
    getList() {
      this.loading = true;
      const params = addDateRange(this.queryParams, this.dateRange);
      listActivity(params).then(response => {
        // 根据实际API返回格式调整
        if (response.data) {
          this.activityList = response.data;
          this.total = response.data.length;
        } else {
          this.activityList = [];
          this.total = 0;
        }
        this.loading = false;
      }).catch(error => {
        console.error("获取活动列表失败:", error);
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        eventId: undefined,
        eventTitle: undefined,
        eventSubTitle: undefined,
        eventCreatorName: undefined,
        eventCreatorAvatar: undefined,
        eventCategory: "创意挑战",
        eventPointCost: 0,
        eventPriceCost: 0,
        isOnline: 1,
        description: undefined,
        startTime: undefined,
        endTime: undefined,
        location: undefined,
        isActive: 1,
        likesCount: 0
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.eventId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加活动";
    },
    /** 查看按钮操作 */
    handleView(row) {
      const eventId = row.eventId;
      getActivity(eventId).then(response => {
        if (response && response.data) {
          this.detailForm = response.data;
          this.openDetail = true;
        } else {
          this.$modal.msgError(response.msg || "获取活动详情失败");
        }
      }).catch(error => {
        console.error("获取活动详情失败:", error);
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const eventId = row.eventId || this.ids[0];
      getActivity(eventId).then(response => {
        if (response && response.data) {
          this.form = response.data;
          this.open = true;
          this.title = "修改活动";
        } else {
          this.$modal.msgError(response.msg || "获取活动信息失败");
        }
      }).catch(error => {
        console.error("获取活动信息失败:", error);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.formLoading = true;
          if (this.form.eventId != undefined) {
            updateActivity(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.formLoading = false;
            }).catch(error => {
              console.error("修改活动失败:", error);
              this.$modal.msgError("修改失败，请重试");
              this.formLoading = false;
            });
          } else {
            addActivity(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.formLoading = false;
            }).catch(error => {
              console.error("新增活动失败:", error);
              this.$modal.msgError("新增失败，请重试");
              this.formLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const eventIds = row.eventId || this.ids;
      this.$modal.confirm('是否确认删除活动编号为"' + eventIds + '"的数据项?').then(() => {
        return delActivity(eventIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 上传图片按钮操作 */
    handleUploadImage(row) {
      this.fileList = [];
      this.uploadForm = {
        eventId: row.eventId,
        eventTitle: row.eventTitle,
        file: null,
        imageDescription: "",
        isMain: false
      };
      this.openUpload = true;
    },
    /** 取消上传 */
    cancelUpload() {
      this.openUpload = false;
      this.fileList = [];
      this.uploadForm.file = null;
      this.uploadForm.imageDescription = "";
      this.uploadForm.isMain = false;
    },
    /** 文件上传前的校验 */
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
        return false;
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    /** 文件数量超出限制时的钩子 */
    handleExceed() {
      this.$message.warning('最多只能上传1张图片');
    },
    /** 自定义上传处理 */
    handleFileUpload(options) {
      this.uploadForm.file = options.file;
      // 不自动上传，等用户点击确定按钮时再上传
    },
    /** 提交上传 */
    submitUpload() {
      if (!this.uploadForm.file) {
        this.$message.error('请选择要上传的图片');
        return;
      }

      this.uploadLoading = true;
      uploadEventImage(
        this.uploadForm.file,
        this.uploadForm.eventId,
        this.uploadForm.imageDescription,
        this.uploadForm.isMain
      ).then(response => {
          if (response.code === 200) {
            this.$modal.msgSuccess("上传成功");
            this.openUpload = false;
            // 如果当前正在查看该活动的详情，则刷新详情
            if (this.openDetail && this.detailForm.eventId === this.uploadForm.eventId) {
              this.handleView({ eventId: this.uploadForm.eventId });
            }
          } else {
            this.$modal.msgError(response.msg || "上传失败");
          }
          this.uploadLoading = false;
        })
        .catch(error => {
          console.error("上传图片失败:", error);
          this.$modal.msgError("上传失败，请重试");
          this.uploadLoading = false;
        });
    }
  }
};
</script>
